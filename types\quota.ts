// 字符配额系统类型定义

export interface UserQuota {
  characters_quota: number;        // 总字符配额
  characters_used: number;         // 已使用字符数
  characters_remaining: number;    // 剩余字符数
  characters_reset_at: string;     // 配额重置时间
  subscription_plan: SubscriptionPlan;
  subscription_status: SubscriptionStatus;
  subscription_expires_at?: string;
  
  // 页数限制
  pages_per_month?: number;        // 月度页数限制（Plus版）
  pages_per_upload?: number;       // 单次上传页数限制（Free版）
  pages_used_this_month?: number;  // 本月已使用页数
}

export type SubscriptionPlan = 'free' | 'plus' | 'pro' | 'elite';

export type SubscriptionStatus = 'active' | 'expired' | 'cancelled' | 'pending';

export interface CharacterUsage {
  id?: number;
  uuid: string;
  user_uuid: string;
  created_at: string;
  characters_used: number;
  operation_type: OperationType;
  source_file_name?: string;
  source_file_size?: number;
  source_pages?: number;
  quiz_questions_count?: number;
  status: UsageStatus;
}

export type OperationType = 
  | 'pdf_processing'     // PDF文本提取
  | 'quiz_generation'    // 测验生成
  | 'question_editing'   // 题目编辑
  | 'export_processing'  // 导出处理
  | 'batch_processing';  // 批量处理

export type UsageStatus = 'completed' | 'failed' | 'processing';

export interface QuotaSnapshot {
  id?: number;
  user_uuid: string;
  snapshot_date: string;
  plan_type: SubscriptionPlan;
  characters_quota: number;
  characters_used: number;
  pages_used: number;
  created_at: string;
}

// 配额检查结果
export interface QuotaCheckResult {
  hasQuota: boolean;
  remainingCharacters: number;
  remainingPages?: number;
  message?: string;
  upgradeRequired?: boolean;
}

// 配额消费请求
export interface ConsumeQuotaRequest {
  user_uuid: string;
  characters_needed: number;
  operation_type: OperationType;
  source_file_name?: string;
  source_file_size?: number;
  source_pages?: number;
  quiz_questions_count?: number;
}

// 配额消费结果
export interface ConsumeQuotaResult {
  success: boolean;
  remainingCharacters: number;
  message?: string;
  usage_uuid?: string;
}

// 订阅计划配置
export interface PlanConfig {
  plan: SubscriptionPlan;
  characters_quota: number;
  pages_per_month: number;
  pages_per_upload: number;
  features: string[];
  price_monthly: number;
  price_yearly?: number;
}

// 预定义的计划配置
export const PLAN_CONFIGS: Record<SubscriptionPlan, PlanConfig> = {
  free: {
    plan: 'free',
    characters_quota: 200000,      // 0.2M 字符
    pages_per_month: 0,            // 无月度页数限制
    pages_per_upload: 5,           // 单次最多5页
    features: [
      'Basic MCQ & True/False questions',
      'Text export only',
      'Learning mode support'
    ],
    price_monthly: 0
  },
  plus: {
    plan: 'plus',
    characters_quota: 5000000,     // 5M 字符
    pages_per_month: 50,           // 月度50页
    pages_per_upload: 0,           // 无单次限制
    features: [
      'Advanced question types',
      'PDF & Word export',
      'Question editing',
      'Email reminders',
      'Priority support'
    ],
    price_monthly: 12
  },
  pro: {
    plan: 'pro',
    characters_quota: 15000000,    // 15M 字符
    pages_per_month: 200,          // 月度200页
    pages_per_upload: 0,           // 无单次限制
    features: [
      'All Plus features',
      'OCR support',
      'Batch processing',
      'Multiple export formats',
      'Share links'
    ],
    price_monthly: 25
  },
  elite: {
    plan: 'elite',
    characters_quota: 30000000,    // 30M 字符
    pages_per_month: 0,            // 无限制
    pages_per_upload: 0,           // 无限制
    features: [
      'All Pro features',
      'Custom branding',
      'API access',
      'Team management',
      'Priority customer service'
    ],
    price_monthly: 45
  }
};

// 字符数估算工具
export interface CharacterEstimate {
  estimatedCharacters: number;
  confidence: 'low' | 'medium' | 'high';
  method: 'file_size' | 'page_count' | 'actual_extraction';
}

// 使用量统计
export interface UsageStats {
  daily: number;
  weekly: number;
  monthly: number;
  total: number;
  averagePerOperation: number;
  topOperations: Array<{
    operation_type: OperationType;
    count: number;
    total_characters: number;
  }>;
}

// 配额警告级别
export type QuotaWarningLevel = 'none' | 'low' | 'medium' | 'high' | 'critical';

export interface QuotaWarning {
  level: QuotaWarningLevel;
  message: string;
  remainingPercentage: number;
  suggestedAction?: string;
}
