import { NextRequest, NextResponse } from 'next/server';
import { PDFService } from '@/services/pdfService';
import { QuizService } from '@/services/quizService';
import { QuizGenerationOptions } from '@/types/quiz';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: '未提供文件' },
        { status: 400 }
      );
    }

    // 获取生成选项（如果有的话）
    const optionsStr = formData.get('options') as string;
    const defaultOptions: QuizGenerationOptions = {
      questionCount: 5,
      questionTypes: ['mcq', 'true_false'],
      difficulty: 'medium',
      language: 'zh',
      includeExplanations: true
    };

    let options = defaultOptions;
    if (optionsStr) {
      try {
        const parsedOptions = JSON.parse(optionsStr);
        options = { ...defaultOptions, ...parsedOptions };
      } catch (e) {
        console.warn('解析选项失败，使用默认选项:', e);
      }
    }

    // 初始化服务
    const pdfService = PDFService.getInstance();
    const quizService = QuizService.getInstance();

    // 验证文件
    const validation = await pdfService.validateFile(file);
    if (!validation.canProcess) {
      return NextResponse.json(
        {
          error: '文件验证失败',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // 处理PDF并提取内容
    const processingResult = await pdfService.processPDF(file);
    if (!processingResult.success || !processingResult.content) {
      return NextResponse.json(
        {
          error: '文件处理失败',
          details: processingResult.error
        },
        { status: 500 }
      );
    }

    // 检查内容质量
    if (processingResult.content.wordCount < 50) {
      return NextResponse.json(
        {
          error: '文档内容太少，无法生成有效的测验题目',
          details: `文档仅包含 ${processingResult.content.wordCount} 个单词，建议至少50个单词`
        },
        { status: 400 }
      );
    }

    // 生成测验
    const quizResult = await quizService.generateQuiz(processingResult.content, options);

    // 验证生成的测验
    const validation_result = quizService.validateQuiz(quizResult.quiz);
    if (!validation_result.isValid) {
      return NextResponse.json(
        {
          error: '生成的测验验证失败',
          details: validation_result.errors
        },
        { status: 500 }
      );
    }

    // 添加源文件信息
    quizResult.quiz.sourceFile = {
      name: file.name,
      type: file.type,
      size: file.size
    };

    return NextResponse.json({
      success: true,
      quiz: quizResult.quiz,
      metadata: {
        processingTime: quizResult.processingTime,
        confidence: quizResult.confidence,
        warnings: quizResult.warnings,
        extractedWordCount: processingResult.content.wordCount,
        fileInfo: validation.fileInfo
      }
    });

  } catch (error) {
    console.error('测验生成错误:', error);

    // 根据错误类型返回不同的错误信息
    let errorMessage = '测验生成失败，请稍后重试';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('AI生成')) {
        errorMessage = 'AI服务暂时不可用，请稍后重试';
        statusCode = 503;
      } else if (error.message.includes('文件')) {
        errorMessage = '文件处理失败，请检查文件格式';
        statusCode = 400;
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error?.toString() : undefined
      },
      { status: statusCode }
    );
  }
}
