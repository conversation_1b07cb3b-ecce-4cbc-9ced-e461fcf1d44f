//import { openai } from '@ai-sdk/openai'; 
import { createOpenRouter } from "@openrouter/ai-sdk-provider";

import { generateObject } from 'ai';
import { z } from 'zod';
import { 
  Quiz, 
  QuizQuestion, 
  QuizGenerationOptions, 
  QuizGenerationResult,
  QuizOption 
} from '@/types/quiz';
import { PDFTextContent } from '@/types/pdf';

/**
 * AI测验生成服务类
 * 负责基于文档内容生成测验题目
 */
export class QuizService {
  private static instance: QuizService;

  public static getInstance(): QuizService {
    if (!QuizService.instance) {
      QuizService.instance = new QuizService();
    }
    return QuizService.instance;
  }

  /**
   * 基于PDF内容生成测验
   */
  public async generateQuiz(
    content: PDFTextContent,
    options: QuizGenerationOptions
  ): Promise<QuizGenerationResult> {
    const startTime = Date.now();

    try {
      // 预处理文本内容
      const processedText = this.preprocessText(content.text, options);
      
      // 生成测验题目
      const quiz = await this.generateQuizFromText(processedText, options);
      
      // 计算处理时间
      const processingTime = Date.now() - startTime;
      
      // 评估生成质量
      const confidence = this.assessQuizQuality(quiz, content);

      return {
        quiz,
        processingTime,
        extractedText: processedText,
        confidence,
        warnings: this.generateWarnings(quiz, content)
      };

    } catch (error) {
      console.error('测验生成错误:', error);
      throw new Error(`测验生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 预处理文本内容
   */
  private preprocessText(text: string, options: QuizGenerationOptions): string {
    let processedText = text;

    // 清理文本
    processedText = processedText
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim();

    // 限制文本长度以避免超出AI模型限制
    const maxLength = 8000; // 约2000个token
    if (processedText.length > maxLength) {
      // 智能截取：优先保留开头和重要段落
      const sections = processedText.split('\n\n');
      let result = '';
      let currentLength = 0;

      for (const section of sections) {
        if (currentLength + section.length <= maxLength) {
          result += section + '\n\n';
          currentLength += section.length + 2;
        } else {
          break;
        }
      }

      processedText = result.trim();
    }

    return processedText;
  }

  /**
   * 使用AI生成测验题目
   */
  private async generateQuizFromText(
    text: string,
    options: QuizGenerationOptions
  ): Promise<Quiz> {
    // 构建AI提示词
    const prompt = this.buildPrompt(text, options);

    // 定义返回数据的schema
    const quizSchema = z.object({
      title: z.string().describe('测验标题'),
      description: z.string().optional().describe('测验描述'),
      questions: z.array(z.object({
        text: z.string().describe('题目文本'),
        type: z.enum(['mcq', 'true_false', 'short_answer']).describe('题目类型'),
        options: z.array(z.object({
          text: z.string().describe('选项文本'),
          isCorrect: z.boolean().describe('是否为正确答案')
        })).optional().describe('选择题选项'),
        correctAnswer: z.string().optional().describe('正确答案'),
        explanation: z.string().optional().describe('答案解释'),
        difficulty: z.enum(['easy', 'medium', 'hard']).describe('题目难度'),
        points: z.number().describe('题目分值'),
        sourceText: z.string().optional().describe('题目来源文本')
      })).describe('测验题目列表')
    });

    try {
      // 创建并使用 OpenRouter 客户端
      const openrouter = createOpenRouter({
        apiKey: process.env.OPENROUTER_API_KEY,
      });
      const { object } = await generateObject({
        model: openrouter('deepseek/deepseek-chat-v3-0324'),
        schema: quizSchema,
        prompt,
        temperature: 0.7,
      });

      // 转换为Quiz对象
      const quiz: Quiz = {
        id: `quiz_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: object.title,
        description: object.description,
        questions: object.questions.map((q, index) => ({
          id: `q_${index + 1}`,
          text: q.text,
          type: q.type,
          options: q.options?.map((opt, optIndex) => ({
            id: String.fromCharCode(97 + optIndex), // a, b, c, d
            text: opt.text,
            isCorrect: opt.isCorrect
          })),
          correctAnswer: q.correctAnswer,
          explanation: q.explanation,
          difficulty: q.difficulty,
          points: q.points,
          sourceText: q.sourceText
        })),
        totalQuestions: object.questions.length,
        totalPoints: object.questions.reduce((sum, q) => sum + q.points, 0),
        estimatedTime: Math.ceil(object.questions.length * 2), // 每题约2分钟
        difficulty: this.calculateOverallDifficulty(object.questions),
        tags: this.extractTags(text),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return quiz;

    } catch (error) {
      console.error('AI生成错误:', error);
      throw new Error('AI生成测验失败，请稍后重试');
    }
  }

  /**
   * 构建AI提示词
   */
  private buildPrompt(text: string, options: QuizGenerationOptions): string {
    const { questionCount, questionTypes, difficulty, language, includeExplanations } = options;

    const languageMap = {
      'zh': '中文',
      'en': 'English'
    };

    const difficultyMap = {
      'easy': '简单',
      'medium': '中等',
      'hard': '困难',
      'mixed': '混合难度'
    };

    return `
请基于以下文档内容生成一个测验。

文档内容：
${text}

生成要求：
- 题目数量：${questionCount}道
- 题目类型：${questionTypes.join('、')}
- 难度级别：${difficultyMap[difficulty]}
- 语言：${languageMap[language]}
- ${includeExplanations ? '需要' : '不需要'}包含答案解释

具体要求：
1. 题目应该基于文档的核心内容和重要概念
2. 选择题应该有4个选项，只有一个正确答案
3. 判断题应该明确表述，避免模糊性
4. 简答题应该考查理解和应用能力
5. 每道题目都应该标注难度级别和分值
6. 如果需要解释，请提供清晰的答案说明
7. 题目应该覆盖文档的不同部分，避免过于集中

请确保生成的测验质量高，题目清晰，答案准确。
    `.trim();
  }

  /**
   * 计算整体难度
   */
  private calculateOverallDifficulty(questions: any[]): 'easy' | 'medium' | 'hard' {
    const difficultyScores = { easy: 1, medium: 2, hard: 3 };
    const totalScore = questions.reduce((sum, q) => sum + difficultyScores[q.difficulty], 0);
    const averageScore = totalScore / questions.length;

    if (averageScore <= 1.3) return 'easy';
    if (averageScore <= 2.3) return 'medium';
    return 'hard';
  }

  /**
   * 从文本中提取标签
   */
  private extractTags(text: string): string[] {
    // 简单的关键词提取逻辑
    const commonWords = new Set(['的', '是', '在', '有', '和', '与', '或', '但', '而', '了', '着', '过', 'the', 'is', 'are', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with']);
    
    const words = text.toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !commonWords.has(word));

    // 统计词频
    const wordCount = new Map<string, number>();
    words.forEach(word => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });

    // 返回出现频率最高的前5个词作为标签
    return Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([word]) => word);
  }

  /**
   * 评估测验质量
   */
  private assessQuizQuality(quiz: Quiz, content: PDFTextContent): number {
    let score = 0.5; // 基础分

    // 题目数量合理性
    if (quiz.questions.length >= 3 && quiz.questions.length <= 20) {
      score += 0.1;
    }

    // 题目类型多样性
    const uniqueTypes = new Set(quiz.questions.map(q => q.type));
    score += uniqueTypes.size * 0.1;

    // 内容覆盖度（简化评估）
    const textLength = content.text.length;
    if (textLength > 1000) {
      score += 0.1;
    }

    // 题目质量（基于文本长度）
    const avgQuestionLength = quiz.questions.reduce((sum, q) => sum + q.text.length, 0) / quiz.questions.length;
    if (avgQuestionLength > 20 && avgQuestionLength < 200) {
      score += 0.1;
    }

    return Math.min(score, 1.0);
  }

  /**
   * 生成警告信息
   */
  private generateWarnings(quiz: Quiz, content: PDFTextContent): string[] {
    const warnings: string[] = [];

    if (quiz.questions.length < 3) {
      warnings.push('题目数量较少，建议增加更多题目');
    }

    if (content.wordCount < 100) {
      warnings.push('文档内容较少，可能影响题目质量');
    }

    const mcqQuestions = quiz.questions.filter(q => q.type === 'mcq');
    const invalidMcq = mcqQuestions.filter(q => !q.options || q.options.length !== 4);
    if (invalidMcq.length > 0) {
      warnings.push('部分选择题选项数量不正确');
    }

    return warnings;
  }

  /**
   * 验证测验数据
   */
  public validateQuiz(quiz: Quiz): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!quiz.title || quiz.title.trim() === '') {
      errors.push('测验标题不能为空');
    }

    if (quiz.questions.length === 0) {
      errors.push('测验必须包含至少一道题目');
    }

    quiz.questions.forEach((question, index) => {
      if (!question.text || question.text.trim() === '') {
        errors.push(`第${index + 1}题的题目文本不能为空`);
      }

      if (question.type === 'mcq') {
        if (!question.options || question.options.length < 2) {
          errors.push(`第${index + 1}题的选择题必须有至少2个选项`);
        } else {
          const correctOptions = question.options.filter(opt => opt.isCorrect);
          if (correctOptions.length !== 1) {
            errors.push(`第${index + 1}题的选择题必须有且仅有一个正确答案`);
          }
        }
      }

      if (question.type === 'true_false' && !question.correctAnswer) {
        errors.push(`第${index + 1}题的判断题必须指定正确答案`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
